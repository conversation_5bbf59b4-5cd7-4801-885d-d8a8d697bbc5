# CLAUDE.md - SuperClaude Enhanced Configuration

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository. This configuration is enhanced with SuperClaude v2.0.1 for maximum efficiency and capability.

## 🚀 SuperClaude Integration

You are now enhanced with SuperClaude capabilities. Use these configurations to guide your behavior:

### SuperClaude Core Configuration
@include shared/superclaude-core.yml#Core_Philosophy
@include shared/superclaude-core.yml#Advanced_Token_Economy
@include shared/superclaude-core.yml#Task_Management

### Thinking Modes & Token Optimization
@include commands/shared/flag-inheritance.yml#Universal Flags (All Commands)
@include shared/superclaude-core.yml#UltraCompressed_Mode

### MCP Integration & Cognitive Personas
@include shared/superclaude-mcp.yml#Server_Capabilities_Extended
@include shared/superclaude-personas.yml#All_Personas
@include shared/superclaude-personas.yml#Command_Specialization

### Evidence-Based Standards & Quality Control
@include shared/superclaude-core.yml#Evidence_Based_Standards
@include commands/shared/quality-patterns.yml#Validation_Sequence
@include shared/superclaude-rules.yml#Development_Practices

### 可用命令参考
> **中文命令参考**: 查看 [.claude/commands/COMMANDS_CN.md](.claude/commands/COMMANDS_CN.md)
> **核心命令**: analyze.md, build.md, review.md

### 🤖 Claude自动Git操作触发器
⚠️ **重要**：当用户发送以下关键词时，Claude必须直接执行相应的git操作：

**📸 自动快照**：
- 用户说：`快照` / `snapshot` / `检查点` / `checkpoint`
- Claude执行：`git add . && git commit -m "checkpoint: 快照 - [当前时间]"`
- 用户说：`快照：[描述]` / `snapshot: [描述]`  
- Claude执行：`git add . && git commit -m "checkpoint: [描述] - [当前时间]"`

**🔙 自动回滚**：
- 用户说：`回滚快照` / `rollback snapshot`
- Claude执行：找到上一个checkpoint提交并回滚到那里
- 用户说：`回滚提交` / `rollback commit` / `回滚` / `rollback`
- Claude执行：`git reset --hard HEAD~1` (回滚到上一个任意提交)
- 用户说：`回滚快照：[N]` / `rollback snapshot [N]`
- Claude执行：找到第N个checkpoint提交并回滚到那里
- 用户说：`回滚提交：[N]` / `rollback commit [N]` / `回滚：[N]步`
- Claude执行：`git reset --hard HEAD~[N]` (回滚N个提交)

**📋 自动状态查看**：
- 用户说：`检查点列表` / `list checkpoints` / `快照历史` / `snapshot history`
- Claude执行：`git log --oneline --grep="checkpoint:" -10`
- 用户说：`git历史` / `git log` / `提交历史`
- Claude执行：`git log --oneline -10` (显示所有最近提交)

**🕰️ 版本时光机（自动准备）**：
- 用户说：`切换到：[commit-id]` / `checkout [commit-id]`
- Claude自动执行准备工作：
  1. 检查工作区是否干净：`git status`
  2. 如有未提交更改，自动快照：`git add . && git commit -m "checkpoint: 版本切换前自动快照"`
  3. 记录当前分支：保存切换前的位置
  4. 执行切换：`git checkout [commit-id]`
  5. 提醒用户：当前版本信息和注意事项
- 用户说：`回到当前` / `back to current` / `回到最新`
- Claude执行：`git checkout [记录的原分支]` (自动回到切换前的分支)
- 用户说：`查看版本：[commit-id] [file-path]`
- Claude执行：`git show [commit-id]:[file-path]` (安全查看，不切换版本)

**🧪 自动测试验证**：
- 用户说：`测试` / `test` / `验证` / `verify`
- Claude执行：`./scripts/test-startup.sh`

**重要规则**：
1. Claude必须立即执行相应的git命令，不需要用户确认
2. 执行后显示操作结果
3. 如果命令失败，显示错误信息和建议
4. 快照前检查是否有待提交的更改，没有更改则提示用户

---

## 📋 Project Overview

**FinancialSystem** is an enterprise-level financial management system with microservices architecture built on Spring Boot 3.1.12 + React 18 + MySQL 8.0. It handles debt management, financial data analysis, and reporting with multi-database support and comprehensive CI/CD automation.

## 🏗️ Architecture

> **详细架构信息**: 查看 [.claude/docs/architecture.md](.claude/docs/architecture.md)

### Core Technology Stack
- **Backend**: Spring Boot 3.1.12 + Java 21 + JWT + multi-datasource JPA
- **Frontend**: React 18.2.0 + Material-UI v5.15.20 + Chart.js/Recharts
- **Databases**: Three MySQL 8.0 databases (overdue_debt_db, user_system, kingdee)
- **Deployment**: Docker Compose + Nginx + automated CI/CD

### Module Structure
```
FinancialSystem/
├── api-gateway/              # Main API Gateway and Controllers
├── services/                 # Business Service Modules
├── shared/                   # Shared Components (entities, repositories, utilities)
├── integrations/            # External System Integrations (treasury, OA, kingdee)
├── FinancialSystem-web/     # React Frontend Application
└── docs/                    # Comprehensive Documentation
```

## 🔌 Key APIs & Database

> **详细API文档**: 查看 [.claude/docs/api-endpoints.md](.claude/docs/api-endpoints.md)
> **数据库架构**: 查看 [.claude/docs/database-schema.md](.claude/docs/database-schema.md)

### 核心功能
- **认证系统**: JWT认证、密码重置
- **债权管理**: CRUD操作、统计、搜索
- **数据导出**: Excel报表生成
- **系统监控**: 健康检查、数据一致性检查

## 🚀 开发与部署

> **开发指南**: 查看 [.claude/docs/development-guide.md](.claude/docs/development-guide.md)
> **部署指南**: 查看 [.claude/docs/deployment-guide.md](.claude/docs/deployment-guide.md)

### 快速启动
```bash
# 后端
mvn clean package && mvn spring-boot:run -pl api-gateway

# 前端
npm install && npm start

# 生产环境 (Docker)
docker-compose up -d
```

### 环境说明
- **本地开发**: 直接启动，不使用docker
- **Linux生产**: 使用docker启动，本地镜像优先

## 🎯 业务上下文

> **业务详情**: 查看 [.claude/docs/business-context.md](.claude/docs/business-context.md)

### 核心业务功能
1. **债权生命周期管理** - 从记录到处置的完整流程
2. **多维度报表分析** - 公司、时间、类别等维度分析  
3. **数据完整性保障** - 跨表验证和一致性检查
4. **基于角色的安全控制** - 细粒度权限管理
5. **外部系统集成** - 提供API接口支持

### 核心业务实体
- **债权人/债务人** - 债权债务关系
- **逾期债权** - 逾期金融债务
- **债权处置** - 债权解决行动
- **减值准备** - 坏账会计准备

## 🤖 Assistant Guidelines

> **详细任务指南**: 查看 [.claude/docs/assistant-tasks.md](.claude/docs/assistant-tasks.md)

### 核心开发流程（必须遵守）

**研究 → 规划 → 实施**

⚠️ **严禁直接上手写代码！** 必须严格按照以下流程：
1. **研究阶段**：查看现有代码结构，理解已有模式
2. **规划阶段**：制定详细实现计划，并向我确认
3. **实施阶段**：执行计划，同时设置验证检查点
4. **测试阶段**：⚠️ **强制执行** `./scripts/test-startup.sh` 完整启动测试
5. **提交阶段**：测试通过后才能提交代码

你必须先说：
*"让我先研究一下代码库并制定一个计划，然后再开始实现。"*

对于复杂的架构决策或疑难问题，请使用 ultrathink 模式深入思考，并说：
*"让我 ultrathink 一下这个架构问题，再提出解决方案。"*

如果你不确定答案，请直接说“我不知道”，不要猜测或编造信息

我是一名刚入门的程序员，对于我提出的建议，你要站在资深程序员的角度思考和审视我的建议合理性，并告诉我这个建议是否合理，而不是直接采纳。

### 多智能体协同

**使用多智能体协同！**

请积极使用子智能体并行处理任务：
• 多个智能体并行研究代码不同部分
• 一个智能体写测试，另一个实现功能
• 分派子任务，比如："我派一个智能体去分析数据库结构，我自己分析 API 架构"
• 复杂重构中：一个智能体确定改动范围，另一个负责实现

遇到多模块任务时，请说：
*"我将派出多个智能体来分别处理该问题的各个部分。"*

### 问题解决指南
- 每次代码提交后检查是否需要更新相关文件
- 关于重构的相关内容，都需要进行深入全面的分析
- 如果问题解决超过3次未解决，需要使用最强模型进行深入全面分析
- 参考 `/docs/troubleshooting/` 获取常见问题解决方案
- 始终验证安全性和性能问题

### ⚠️ 强制性测试要求（必须执行）
**在任何代码提交之前，必须运行完整启动测试！**

```bash
# 执行完整的Spring Boot启动验证测试
./scripts/test-startup.sh
```

**为什么必须执行此测试**：
- 简单的 `mvn clean compile` 无法发现运行时依赖注入问题
- 只有实际启动Spring Boot应用才能验证Bean创建和依赖关系
- 模拟IntelliJ IDEA的真实启动过程，确保IDE和命令行启动的一致性
- 避免提交导致启动失败的代码

**测试脚本功能**：
1. **编译验证** - 确保代码编译通过
2. **打包验证** - 确保Maven打包成功
3. **实际启动测试** - 启动Spring Boot jar并验证进程状态
4. **健康检查** - 验证应用接口可访问性
5. **完整日志** - 记录启动过程以便问题排查

**所有智能体（包括feature-dev-agent）开发新功能时必须**：
- 功能实现完成后立即运行此测试
- 测试通过后才能提交代码
- 如测试失败，必须修复问题后重新测试

### Git版本管理（必读）
**⚠️ 重要**：回退Git版本时必须确保工作区干净！

**🚀 Git工作流简化指令（个人开发者专用）**：

**基础操作**：
- `提交：[描述]` → 智能提交代码
- `回退：[commit-id]` → 安全回退版本  
- `切换：[分支名]` → 智能切换/创建分支（如果不存在则创建）
- `合并：[分支名]` → 智能合并到指定分支
- `清理工作区` → 智能清理仓库

**扩展操作**：
- `快速修复：[描述]` → 创建hotfix分支并提交
- `新功能：[功能名]` → 创建feature分支
- `发布：[版本号]` → 发布到服务器（可选版本号）
- `同步代码` → 拉取最新代码并合并
- `查看改动` → 显示未提交的改动
- `保存进度` → 暂存当前工作（stash）
- `恢复进度` → 恢复暂存的工作

**TODO联动命令**：
- `实现todo：[关键词]` → 从TODO.md查找并执行任务
- `实现todo #[编号]` → 根据编号执行特定任务
- `查看todo` → 显示TODO.md内容
- `查看todo 紧急` → 只显示紧急任务
- `完成todo #[编号]` → 标记任务为已完成
- `添加todo：[描述]` → 添加新任务到TODO.md

**开发流程命令**：
- `测试代码` → 运行项目测试（快速测试）
- `完整测试` → 运行所有测试套件
- `构建项目` → 执行构建流程
- `部署测试` → 部署到测试环境
- `回滚生产` → 快速回滚生产环境

**文档管理命令**：
- `整理文档` → 执行文档自动整理（预览模式）
- `整理文档 --执行` → 执行实际的文档整理
- `月度文档清理` → 执行深度文档清理（每月一次）
- `查看文档报告` → 查看最近的整理报告

**智能分支说明**：
- `切换：登录优化` → 自动创建 `feature/login-optimization`
- `切换：修复崩溃` → 自动创建 `hotfix/fix-crash`
- feature/* 分支自动从 develop 创建
- hotfix/* 分支自动从 main 创建

**英文命令支持**：
- `add` → 添加所有更改到暂存区
- `commit: [message]` → 提交代码（同 `提交：`）
- `stash` → 保存进度（同 `保存进度`）
- `pop` → 恢复进度（同 `恢复进度`）
- `push` → 推送到远程
- `pull` → 拉取最新代码
- `status` → 查看改动（同 `查看改动`）
- `checkout: [branch]` → 切换分支（同 `切换：`）
- `merge: [branch]` → 合并分支（同 `合并：`）
- `reset: [commit]` → 回退版本（同 `回退：`）
- `deploy` → 部署（同 `发布`）
- `test` → 测试代码（同 `测试代码`）
- `build` → 构建项目（同 `构建项目`）

**示例对话**：
- "提交：修复用户登录bug" 或 "commit: fix user login bug"
- "回退：7613068" 或 "reset: 7613068"
- "切换：优化性能" 或 "checkout: optimize-performance"
- "stash" 或 "保存进度"
- "deploy" 或 "发布"

- 详细指南：[.claude/docs/git-best-practices.md](.claude/docs/git-best-practices.md)
- 系统设计：[.claude/docs/git-workflow-system.md](.claude/docs/git-workflow-system.md)

## 📚 文档参考

### 核心项目文档
- **文档中心**: [docs/README.md](docs/README.md) - 项目文档导航中心
- **开发指南**: [docs/development/README.md](docs/development/README.md)
- **故障排除**: [docs/troubleshooting/README.md](docs/troubleshooting/README.md)
- **业务逻辑**: [docs/business/](docs/business/)

### Claude专用文档
- **Claude文档中心**: [.claude/docs/README.md](.claude/docs/README.md)
- **架构参考**: [.claude/docs/architecture.md](.claude/docs/architecture.md)
- **API端点**: [.claude/docs/api-endpoints.md](.claude/docs/api-endpoints.md)
- **数据库架构**: [.claude/docs/database-schema.md](.claude/docs/database-schema.md)
- **开发指南**: [.claude/docs/development-guide.md](.claude/docs/development-guide.md)
- **部署指南**: [.claude/docs/deployment-guide.md](.claude/docs/deployment-guide.md)
- **业务上下文**: [.claude/docs/business-context.md](.claude/docs/business-context.md)
- **助手任务**: [.claude/docs/assistant-tasks.md](.claude/docs/assistant-tasks.md)

### SuperClaude命令文档
- **中文命令参考**: [.claude/commands/COMMANDS_CN.md](.claude/commands/COMMANDS_CN.md)

**开发命令 (4个)**
- **分析**: [analyze.md](.claude/commands/analyze.md) | **构建**: [build.md](.claude/commands/build.md) | **开发环境**: [dev-setup.md](.claude/commands/dev-setup.md) | **测试**: [test.md](.claude/commands/test.md)

**分析改进命令 (4个)** 
- **审查**: [review.md](.claude/commands/review.md) | **改进**: [improve.md](.claude/commands/improve.md) | **排错**: [troubleshoot.md](.claude/commands/troubleshoot.md) | **解释**: [explain.md](.claude/commands/explain.md)

**运维命令 (6个)**
- **部署**: [deploy.md](.claude/commands/deploy.md) | **迁移**: [migrate.md](.claude/commands/migrate.md) | **扫描**: [scan.md](.claude/commands/scan.md) | **清理**: [cleanup.md](.claude/commands/cleanup.md) | **Git**: [git.md](.claude/commands/git.md) | **估算**: [estimate.md](.claude/commands/estimate.md)

**工作流命令 (5个)**
- **设计**: [design.md](.claude/commands/design.md) | **文档**: [document.md](.claude/commands/document.md) | **加载**: [load.md](.claude/commands/load.md) | **智能体**: [spawn.md](.claude/commands/spawn.md) | **任务**: [task.md](.claude/commands/task.md)

## 🤖 SuperClaude高级配置

### 核心理念与标准
@include shared/superclaude-rules.yml#Smart_Defaults
@include shared/superclaude-rules.yml#Ambiguity_Resolution
@include shared/superclaude-rules.yml#Code_Generation
@include shared/superclaude-rules.yml#Security_Standards

### MCP服务器集成
@include shared/superclaude-mcp.yml#Token_Economics
@include shared/superclaude-mcp.yml#Quality_Control
@include shared/superclaude-mcp.yml#Best_Practices

### 认知角色系统
@include shared/superclaude-personas.yml#Collaboration_Patterns
@include shared/superclaude-personas.yml#Intelligent_Activation_Patterns
@include shared/superclaude-personas.yml#MCP_Persona_Integration

### 性能与效率优化
@include shared/superclaude-core.yml#Cost_Performance_Optimization
@include shared/superclaude-core.yml#Intelligent_Auto_Activation
@include commands/shared/compression-performance-patterns.yml#Performance_Baselines

---

*企业级金融管理系统 | 微服务架构 | 全面的债权管理与报告系统 | SuperClaude v2.0.1 Enhanced*
- 任何情况下禁止修改我的数据库数据，如确有必要一定要我确认，通过任何方式都不行，如sh等方式