import { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  Alert,
  CircularProgress,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
} from '@mui/material';
import { Warning, Refresh, Extension } from '@mui/icons-material';
import { format, differenceInDays, parseISO } from 'date-fns';
import { zhCN } from 'date-fns/locale';
import { useAuth } from 'context/AuthContext';

/**
 * 即将过期权限组件
 */
function ExpiringPermissions({ onError }) {
  const { user } = useAuth();
  const [permissions, setPermissions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [extendDialog, setExtendDialog] = useState({ open: false, permission: null });
  const [extendDays, setExtendDays] = useState(30);

  // 获取即将过期的权限
  const fetchExpiringPermissions = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/data-permissions/expiring', {
        headers: {
          Authorization: `Bearer ${user?.token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('获取即将过期权限失败');
      }

      const data = await response.json();
      setPermissions(data || []);
    } catch (error) {
      console.error('获取即将过期权限失败:', error);
      onError?.(error.message || '获取即将过期权限失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (user?.token) {
      fetchExpiringPermissions();
    }
  }, [user]);

  // 延期权限
  const handleExtendPermission = async () => {
    if (!extendDialog.permission || !extendDays) {
      return;
    }

    try {
      const response = await fetch(`/api/data-permissions/${extendDialog.permission.id}/extend`, {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${user?.token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ days: extendDays }),
      });

      if (!response.ok) {
        throw new Error('延期权限失败');
      }

      setExtendDialog({ open: false, permission: null });
      setExtendDays(30);
      await fetchExpiringPermissions(); // 刷新列表
    } catch (error) {
      console.error('延期权限失败:', error);
      onError?.(error.message || '延期权限失败');
    }
  };

  // 获取剩余天数的颜色
  const getDaysColor = days => {
    if (days <= 3) {
      return 'error';
    }
    if (days <= 7) {
      return 'warning';
    }
    return 'info';
  };

  // 格式化日期
  const formatDate = dateString => {
    try {
      return format(parseISO(dateString), 'yyyy-MM-dd', { locale: zhCN });
    } catch {
      return dateString;
    }
  };

  // 计算剩余天数
  const calculateRemainingDays = expiryDate => {
    try {
      return differenceInDays(parseISO(expiryDate), new Date());
    } catch {
      return 0;
    }
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" my={3}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
        <Typography variant="h6" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Warning color="warning" />
          即将过期权限 ({permissions.length})
        </Typography>
        <Button
          variant="outlined"
          startIcon={<Refresh />}
          onClick={fetchExpiringPermissions}
          size="small"
        >
          刷新
        </Button>
      </Box>

      {permissions.length === 0 ? (
        <Alert severity="info">暂无即将过期的权限</Alert>
      ) : (
        <>
          <Alert severity="warning" sx={{ mb: 2 }}>
            以下权限将在30天内过期，请及时处理
          </Alert>

          <TableContainer component={Paper} elevation={0} sx={{ border: '1px solid #e0e0e0' }}>
            <Table size="small">
              <TableHead>
                <TableRow sx={{ backgroundColor: '#f5f5f5' }}>
                  <TableCell>用户</TableCell>
                  <TableCell>公司</TableCell>
                  <TableCell>权限类型</TableCell>
                  <TableCell>过期时间</TableCell>
                  <TableCell>剩余天数</TableCell>
                  <TableCell>操作</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {permissions.map(permission => {
                  const remainingDays = calculateRemainingDays(permission.expiryDate);
                  return (
                    <TableRow key={permission.id} hover>
                      <TableCell>{permission.userName}</TableCell>
                      <TableCell>{permission.companyName}</TableCell>
                      <TableCell>
                        <Chip
                          label={permission.permissionType === 'READ' ? '只读' : '读写'}
                          size="small"
                          color={permission.permissionType === 'READ' ? 'default' : 'primary'}
                        />
                      </TableCell>
                      <TableCell>{formatDate(permission.expiryDate)}</TableCell>
                      <TableCell>
                        <Chip
                          label={`${remainingDays}天`}
                          size="small"
                          color={getDaysColor(remainingDays)}
                        />
                      </TableCell>
                      <TableCell>
                        <Button
                          size="small"
                          startIcon={<Extension />}
                          onClick={() => setExtendDialog({ open: true, permission })}
                        >
                          延期
                        </Button>
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          </TableContainer>
        </>
      )}

      {/* 延期对话框 */}
      <Dialog
        open={extendDialog.open}
        onClose={() => setExtendDialog({ open: false, permission: null })}
      >
        <DialogTitle>延期权限</DialogTitle>
        <DialogContent>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
            为用户 <strong>{extendDialog.permission?.userName}</strong> 在公司{' '}
            <strong>{extendDialog.permission?.companyName}</strong> 的权限延期
          </Typography>
          <TextField
            label="延期天数"
            type="number"
            value={extendDays}
            onChange={e => setExtendDays(parseInt(e.target.value) || 0)}
            fullWidth
            inputProps={{ min: 1, max: 365 }}
            helperText="请输入1-365天"
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setExtendDialog({ open: false, permission: null })}>取消</Button>
          <Button onClick={handleExtendPermission} variant="contained">
            确认延期
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
}

export default ExpiringPermissions;
